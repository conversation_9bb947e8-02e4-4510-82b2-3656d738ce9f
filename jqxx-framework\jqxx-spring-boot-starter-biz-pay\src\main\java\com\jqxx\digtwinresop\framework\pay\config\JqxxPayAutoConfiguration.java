package com.jqxx.digtwinresop.framework.pay.config;

import com.jqxx.digtwinresop.framework.pay.core.client.PayClientFactory;
import com.jqxx.digtwinresop.framework.pay.core.client.impl.PayClientFactoryImpl;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.context.annotation.Bean;

/**
 * 支付配置类
 *
 * <AUTHOR>
 */
@AutoConfiguration
public class JqxxPayAutoConfiguration {

    @Bean
    public PayClientFactory payClientFactory() {
        return new PayClientFactoryImpl();
    }

}
