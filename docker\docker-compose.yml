version: '3'
services:
  jqxx-gateway:
    image: jqxx-gateway
    container_name: jqxx-gateway
    environment:
      - TZ=Asia/Shanghai # 配置程序默认时区为上海（中国标准时间）
      - JAVA_TOOL_OPTIONS=-javaagent:/data/skywalking/skywalking-agent/skywalking-agent.jar # 配置skywalking
      - SW_AGENT_NAME=jqxx-gateway
      - SW_AGENT_TRACE_IGNORE_PATH=Redisson/PING,/actuator/**,/admin/**
      - SW_AGENT_COLLECTOR_BACKEND_SERVICES=[YOUR_SKYWALKING_ADDR] # 请替换 your.skywalking.addr 为你的 skywalking 地址
      - SPRING_PROFILES_ACTIVE=test # 指定程序运行环境
      - SPRING_CLOUD_NACOS_CONFIG_SERVER_ADDR=[YOUR_NACOS_ADDR] # 配置中心地址
      - SPRING_CLOUD_NACOS_CONFIG_NAMESPACE=[YOUR_NAMESPACE] # 命名空间
      - SPRING_CLOUD_NACOS_SERVER_ADDR=[YOUR_NACOS_ADDR]  # 注册中心地址
      - SPRING_CLOUD_NACOS_DISCOVERY_NAMESPACE=[YOUR_NAMESPACE] # 命名空间
    volumes:
      - /docker/jqxx-cloud/logs:/root/logs/
      - /data/skywalking/skywalking-agent:/data/skywalking/skywalking-agent
    restart: always
    network_mode: host # 以主机网络环境运行
  jqxx-system:
    image: jqxx-module-system-biz
    container_name: jqxx-system
    environment:
      - TZ=Asia/Shanghai # 配置程序默认时区为上海（中国标准时间）
      - JAVA_TOOL_OPTIONS=-javaagent:/data/skywalking/skywalking-agent/skywalking-agent.jar # 配置skywalking
      - SW_AGENT_NAME=jqxx-gateway
      - SW_AGENT_TRACE_IGNORE_PATH=Redisson/PING,/actuator/**,/admin/**
      - SW_AGENT_COLLECTOR_BACKEND_SERVICES=[YOUR_SKYWALKING_ADDR] # 请替换 your.skywalking.addr 为你的 skywalking 地址
      - SPRING_PROFILES_ACTIVE=test # 指定程序运行环境
      - SPRING_CLOUD_NACOS_CONFIG_SERVER_ADDR=[YOUR_NACOS_ADDR] # 配置中心地址
      - SPRING_CLOUD_NACOS_CONFIG_NAMESPACE=[YOUR_NAMESPACE] # 命名空间
      - SPRING_CLOUD_NACOS_SERVER_ADDR=[YOUR_NACOS_ADDR]  # 注册中心地址
      - SPRING_CLOUD_NACOS_DISCOVERY_NAMESPACE=[YOUR_NAMESPACE] # 命名空间
    volumes:
      - /docker/jqxx-cloud/logs:/root/logs/
      - /data/skywalking/skywalking-agent:/data/skywalking/skywalking-agent
    healthcheck:
      test: [ "CMD","curl","-f","http://localhost:48081" ]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s
    restart: always
    network_mode: host
  jqxx-infra:
    image: jqxx-module-infra-biz
    container_name: jqxx-infra
    environment:
      - TZ=Asia/Shanghai # 配置程序默认时区为上海（中国标准时间）
      - JAVA_TOOL_OPTIONS=-javaagent:/data/skywalking/skywalking-agent/skywalking-agent.jar # 配置skywalking
      - SW_AGENT_NAME=jqxx-gateway
      - SW_AGENT_TRACE_IGNORE_PATH=Redisson/PING,/actuator/**,/admin/**
      - SW_AGENT_COLLECTOR_BACKEND_SERVICES=[YOUR_SKYWALKING_ADDR] # 请替换 your.skywalking.addr 为你的 skywalking 地址
      - SPRING_PROFILES_ACTIVE=test # 指定程序运行环境
      - SPRING_CLOUD_NACOS_CONFIG_SERVER_ADDR=[YOUR_NACOS_ADDR] # 配置中心地址
      - SPRING_CLOUD_NACOS_CONFIG_NAMESPACE=[YOUR_NAMESPACE] # 命名空间
      - SPRING_CLOUD_NACOS_SERVER_ADDR=[YOUR_NACOS_ADDR]  # 注册中心地址
      - SPRING_CLOUD_NACOS_DISCOVERY_NAMESPACE=[YOUR_NAMESPACE] # 命名空间
    volumes:
      - /docker/jqxx-cloud/logs:/root/logs/
      - /data/skywalking/skywalking-agent:/data/skywalking/skywalking-agent
    restart: always
    network_mode: host
    healthcheck:
      test: [ "CMD","curl","-f","http://localhost:48082" ]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s
    depends_on:
      jqxx-system:
        condition: service_healthy
  jqxx-report:
    image: jqxx-module-report-biz
    container_name: jqxx-report
    environment:
      - TZ=Asia/Shanghai # 配置程序默认时区为上海（中国标准时间）
      - JAVA_TOOL_OPTIONS=-javaagent:/data/skywalking/skywalking-agent/skywalking-agent.jar # 配置skywalking
      - SW_AGENT_NAME=jqxx-gateway
      - SW_AGENT_TRACE_IGNORE_PATH=Redisson/PING,/actuator/**,/admin/**
      - SW_AGENT_COLLECTOR_BACKEND_SERVICES=[YOUR_SKYWALKING_ADDR] # 请替换 your.skywalking.addr 为你的 skywalking 地址
      - SPRING_PROFILES_ACTIVE=test # 指定程序运行环境
      - SPRING_CLOUD_NACOS_CONFIG_SERVER_ADDR=[YOUR_NACOS_ADDR] # 配置中心地址
      - SPRING_CLOUD_NACOS_CONFIG_NAMESPACE=[YOUR_NAMESPACE] # 命名空间
      - SPRING_CLOUD_NACOS_SERVER_ADDR=[YOUR_NACOS_ADDR]  # 注册中心地址
      - SPRING_CLOUD_NACOS_DISCOVERY_NAMESPACE=[YOUR_NAMESPACE] # 命名空间
    volumes:
      - /docker/jqxx-cloud/logs:/root/logs/
      - /data/skywalking/skywalking-agent:/data/skywalking/skywalking-agent
    restart: always
    network_mode: host
    depends_on:
      jqxx-infra:
        condition: service_healthy
  jqxx-bpm:
    image: jqxx-module-bpm-biz
    container_name: jqxx-bpm
    environment:
      - TZ=Asia/Shanghai # 配置程序默认时区为上海（中国标准时间）
      - JAVA_TOOL_OPTIONS=-javaagent:/data/skywalking/skywalking-agent/skywalking-agent.jar # 配置skywalking
      - SW_AGENT_NAME=jqxx-gateway
      - SW_AGENT_TRACE_IGNORE_PATH=Redisson/PING,/actuator/**,/admin/**
      - SW_AGENT_COLLECTOR_BACKEND_SERVICES=[YOUR_SKYWALKING_ADDR] # 请替换 your.skywalking.addr 为你的 skywalking 地址
      - SPRING_PROFILES_ACTIVE=test # 指定程序运行环境
      - SPRING_CLOUD_NACOS_CONFIG_SERVER_ADDR=[YOUR_NACOS_ADDR] # 配置中心地址
      - SPRING_CLOUD_NACOS_CONFIG_NAMESPACE=[YOUR_NAMESPACE] # 命名空间
      - SPRING_CLOUD_NACOS_SERVER_ADDR=[YOUR_NACOS_ADDR]  # 注册中心地址
      - SPRING_CLOUD_NACOS_DISCOVERY_NAMESPACE=[YOUR_NAMESPACE] # 命名空间
    volumes:
      - /docker/jqxx-cloud/logs:/root/logs/
      - /data/skywalking/skywalking-agent:/data/skywalking/skywalking-agent
    restart: always
    network_mode: host
    depends_on:
      jqxx-infra:
        condition: service_healthy
  jqxx-pay:
    image: jqxx-module-pay-biz
    container_name: jqxx-pay
    environment:
      - TZ=Asia/Shanghai # 配置程序默认时区为上海（中国标准时间）
      - JAVA_TOOL_OPTIONS=-javaagent:/data/skywalking/skywalking-agent/skywalking-agent.jar # 配置skywalking
      - SW_AGENT_NAME=jqxx-gateway
      - SW_AGENT_TRACE_IGNORE_PATH=Redisson/PING,/actuator/**,/admin/**
      - SW_AGENT_COLLECTOR_BACKEND_SERVICES=[YOUR_SKYWALKING_ADDR] # 请替换 your.skywalking.addr 为你的 skywalking 地址
      - SPRING_PROFILES_ACTIVE=test # 指定程序运行环境
      - SPRING_CLOUD_NACOS_CONFIG_SERVER_ADDR=[YOUR_NACOS_ADDR] # 配置中心地址
      - SPRING_CLOUD_NACOS_CONFIG_NAMESPACE=[YOUR_NAMESPACE] # 命名空间
      - SPRING_CLOUD_NACOS_SERVER_ADDR=[YOUR_NACOS_ADDR]  # 注册中心地址
      - SPRING_CLOUD_NACOS_DISCOVERY_NAMESPACE=[YOUR_NAMESPACE] # 命名空间
    volumes:
      - /docker/jqxx-cloud/logs:/root/logs/
      - /data/skywalking/skywalking-agent:/data/skywalking/skywalking-agent
    restart: always
    network_mode: host
    depends_on:
      jqxx-infra:
        condition: service_healthy
  jqxx-mp:
    image: jqxx-module-mp-biz
    container_name: jqxx-mp
    environment:
      - TZ=Asia/Shanghai # 配置程序默认时区为上海（中国标准时间）
      - JAVA_TOOL_OPTIONS=-javaagent:/data/skywalking/skywalking-agent/skywalking-agent.jar # 配置skywalking
      - SW_AGENT_NAME=jqxx-gateway
      - SW_AGENT_TRACE_IGNORE_PATH=Redisson/PING,/actuator/**,/admin/**
      - SW_AGENT_COLLECTOR_BACKEND_SERVICES=[YOUR_SKYWALKING_ADDR] # 请替换 your.skywalking.addr 为你的 skywalking 地址
      - SPRING_PROFILES_ACTIVE=test # 指定程序运行环境
      - SPRING_CLOUD_NACOS_CONFIG_SERVER_ADDR=[YOUR_NACOS_ADDR] # 配置中心地址
      - SPRING_CLOUD_NACOS_CONFIG_NAMESPACE=[YOUR_NAMESPACE] # 命名空间
      - SPRING_CLOUD_NACOS_SERVER_ADDR=[YOUR_NACOS_ADDR]  # 注册中心地址
      - SPRING_CLOUD_NACOS_DISCOVERY_NAMESPACE=[YOUR_NAMESPACE] # 命名空间
    volumes:
      - /docker/jqxx-cloud/logs:/root/logs/
      - /data/skywalking/skywalking-agent:/data/skywalking/skywalking-agent
    restart: always
    network_mode: host
    depends_on:
      jqxx-infra:
        condition: service_healthy