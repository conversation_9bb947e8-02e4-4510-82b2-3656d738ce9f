# 前端AES加密使用说明

## 概述
这个文档说明了如何在前端实现AES加密，与后端Java的AESUtil.java保持一致。

## 依赖安装

### 1. npm项目中安装crypto-js
```bash
npm install crypto-js
```

### 2. 在HTML中直接引入CDN
```html
<script src="https://cdnjs.cloudflare.com/ajax/libs/crypto-js/4.1.1/crypto-js.min.js"></script>
```

## 核心配置参数

与后端保持一致的参数：
- **密钥**: `603deb1015ca71be2b73aef0857d778140873d0a`
- **IV**: `2b7e151628aed2a6abf7158809cf4f3c`
- **算法**: AES-CBC
- **填充**: PKCS7Padding
- **编码**: Base64

## 快速使用

### 1. 基本加密示例
```javascript
import AESUtil from './utils/AESUtil';

// 加密密码
const password = "admin123";
const encryptedPassword = AESUtil.encrypt(password);
console.log("加密后:", encryptedPassword);

// 发送到后端
const loginData = {
    username: "admin",
    password: encryptedPassword
};
```

### 2. Vue.js中使用
```javascript
// 在组件中
methods: {
    async login() {
        try {
            const encryptedPassword = AESUtil.encrypt(this.password);
            
            const response = await this.$http.post('/system/auth/login', {
                username: this.username,
                password: encryptedPassword
            });
            
            if (response.data.code === 0) {
                this.$message.success('登录成功');
                this.$router.push('/dashboard');
            }
        } catch (error) {
            this.$message.error('登录失败');
        }
    }
}
```

### 3. React中使用
```javascript
import { useState } from 'react';
import AESUtil from './utils/AESUtil';

function LoginForm() {
    const [username, setUsername] = useState('');
    const [password, setPassword] = useState('');

    const handleLogin = async () => {
        try {
            const encryptedPassword = AESUtil.encrypt(password);
            
            const response = await fetch('/system/auth/login', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    username,
                    password: encryptedPassword
                })
            });
            
            const data = await response.json();
            if (data.code === 0) {
                console.log('登录成功');
            }
        } catch (error) {
            console.error('登录失败:', error);
        }
    };

    return (
        <div>
            <input 
                value={username} 
                onChange={(e) => setUsername(e.target.value)} 
                placeholder="用户名" 
            />
            <input 
                type="password"
                value={password} 
                onChange={(e) => setPassword(e.target.value)} 
                placeholder="密码" 
            />
            <button onClick={handleLogin}>登录</button>
        </div>
    );
}
```

### 4. 原生JavaScript使用
```javascript
// 登录表单处理
document.getElementById('loginForm').addEventListener('submit', async (e) => {
    e.preventDefault();
    
    const username = document.getElementById('username').value;
    const password = document.getElementById('password').value;
    
    try {
        // 加密密码
        const encryptedPassword = AESUtil.encrypt(password);
        
        // 发送请求
        const response = await fetch('/system/auth/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                username: username,
                password: encryptedPassword
            })
        });
        
        const result = await response.json();
        
        if (result.code === 0) {
            alert('登录成功');
            // 跳转到首页
            window.location.href = '/dashboard';
        } else {
            alert('登录失败: ' + result.msg);
        }
    } catch (error) {
        console.error('请求失败:', error);
        alert('网络错误，请重试');
    }
});
```

## 注意事项

1. **密钥安全**: 虽然密钥在前端是可见的，但这种加密主要是为了防止密码在传输过程中被明文截获。

2. **HTTPS**: 建议在生产环境中使用HTTPS来进一步保护数据传输安全。

3. **错误处理**: 加密过程可能会失败，需要适当的错误处理。

4. **兼容性**: crypto-js库支持大多数现代浏览器，但在老旧浏览器中可能需要polyfill。

## 测试验证

可以使用提供的`login-demo.html`文件来测试加密功能：

1. 打开`login-demo.html`文件
2. 输入用户名和密码
3. 点击"测试加密"按钮验证加密解密是否正常
4. 点击"模拟登录"按钮查看发送到后端的数据格式

## 常见问题

### Q: 加密后的密码每次都不一样吗？
A: 不是的，使用固定的IV，相同的明文密码会产生相同的加密结果。

### Q: 如何验证前后端加密解密是否一致？
A: 可以在前端加密一个测试密码，然后在后端使用相同的密码进行解密测试。

### Q: 密钥可以动态获取吗？
A: 可以，但需要确保前后端使用相同的密钥。可以通过API获取密钥，但要注意安全性。

## 完整的工具类文件

参考 `frontend-aes-encryption.js` 文件，其中包含了完整的AES工具类实现和使用示例。
