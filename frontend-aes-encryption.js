// 前端AES加密工具 - 对应后端AESUtil.java
// 需要引入 crypto-js 库: npm install crypto-js

import CryptoJS from 'crypto-js';

class AESUtil {
    // 与后端保持一致的常量
    static FIXED_SALT = "2b7e151628aed2a6abf7158809cf4f3c";
    static SECRET_KEY = "603deb1015ca71be2b73aef0857d778140873d0a";

    /**
     * 加密密码 - 对应后端的decrypt方法
     * @param {string} plainText 明文密码
     * @returns {string} 加密后的Base64字符串
     */
    static encrypt(plainText) {
        try {
            // 将密钥和IV转换为UTF-8字节（与后端保持一致）
            const key = CryptoJS.enc.Utf8.parse(this.SECRET_KEY);
            const iv = CryptoJS.enc.Utf8.parse(this.FIXED_SALT);

            // 使用AES-CBC模式加密
            const encrypted = CryptoJS.AES.encrypt(plainText, key, {
                iv: iv,
                mode: CryptoJS.mode.CBC,
                padding: CryptoJS.pad.Pkcs7
            });

            // 返回Base64编码的结果
            return encrypted.toString();
        } catch (error) {
            console.error('AES加密失败:', error);
            throw new Error('密码加密失败');
        }
    }

    /**
     * 解密密码 - 用于测试验证
     * @param {string} encryptedData 加密的Base64字符串
     * @returns {string} 解密后的明文
     */
    static decrypt(encryptedData) {
        try {
            // 将密钥和IV转换为UTF-8字节
            const key = CryptoJS.enc.Utf8.parse(this.SECRET_KEY);
            const iv = CryptoJS.enc.Utf8.parse(this.FIXED_SALT);

            // 解密
            const decrypted = CryptoJS.AES.decrypt(encryptedData, key, {
                iv: iv,
                mode: CryptoJS.mode.CBC,
                padding: CryptoJS.pad.Pkcs7
            });

            // 转换为UTF-8字符串
            return decrypted.toString(CryptoJS.enc.Utf8);
        } catch (error) {
            console.error('AES解密失败:', error);
            throw new Error('密码解密失败');
        }
    }
}

// 使用示例
export default AESUtil;

// ========== 使用示例 ==========

// 1. 在登录表单中使用
function handleLogin(username, password) {
    try {
        // 加密密码
        const encryptedPassword = AESUtil.encrypt(password);
        
        // 发送登录请求
        const loginData = {
            username: username,
            password: encryptedPassword  // 发送加密后的密码
        };
        
        // 调用登录API
        fetch('/system/auth/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(loginData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.code === 0) {
                console.log('登录成功');
                // 处理登录成功逻辑
            } else {
                console.error('登录失败:', data.msg);
            }
        })
        .catch(error => {
            console.error('请求失败:', error);
        });
        
    } catch (error) {
        console.error('密码加密失败:', error);
        alert('登录失败，请重试');
    }
}

// 2. Vue.js 组件中使用示例
/*
<template>
  <div class="login-form">
    <input v-model="username" placeholder="用户名" />
    <input v-model="password" type="password" placeholder="密码" />
    <button @click="login">登录</button>
  </div>
</template>

<script>
import AESUtil from './utils/AESUtil';

export default {
  data() {
    return {
      username: '',
      password: ''
    };
  },
  methods: {
    async login() {
      try {
        // 加密密码
        const encryptedPassword = AESUtil.encrypt(this.password);
        
        // 发送登录请求
        const response = await this.$http.post('/system/auth/login', {
          username: this.username,
          password: encryptedPassword
        });
        
        if (response.data.code === 0) {
          this.$message.success('登录成功');
          // 跳转到首页
          this.$router.push('/dashboard');
        } else {
          this.$message.error(response.data.msg);
        }
      } catch (error) {
        console.error('登录失败:', error);
        this.$message.error('登录失败，请重试');
      }
    }
  }
};
</script>
*/

// 3. React 组件中使用示例
/*
import React, { useState } from 'react';
import AESUtil from './utils/AESUtil';

function LoginForm() {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');

  const handleLogin = async () => {
    try {
      // 加密密码
      const encryptedPassword = AESUtil.encrypt(password);
      
      // 发送登录请求
      const response = await fetch('/system/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          username: username,
          password: encryptedPassword
        })
      });
      
      const data = await response.json();
      
      if (data.code === 0) {
        console.log('登录成功');
        // 处理登录成功逻辑
      } else {
        console.error('登录失败:', data.msg);
      }
    } catch (error) {
      console.error('登录失败:', error);
    }
  };

  return (
    <div>
      <input 
        value={username} 
        onChange={(e) => setUsername(e.target.value)} 
        placeholder="用户名" 
      />
      <input 
        type="password"
        value={password} 
        onChange={(e) => setPassword(e.target.value)} 
        placeholder="密码" 
      />
      <button onClick={handleLogin}>登录</button>
    </div>
  );
}

export default LoginForm;
*/

// 4. 测试函数
function testEncryption() {
    const testPassword = "admin123";
    console.log("原始密码:", testPassword);
    
    // 加密
    const encrypted = AESUtil.encrypt(testPassword);
    console.log("加密后:", encrypted);
    
    // 解密验证
    const decrypted = AESUtil.decrypt(encrypted);
    console.log("解密后:", decrypted);
    
    console.log("加密解密是否一致:", testPassword === decrypted);
}

// 运行测试
// testEncryption();
