<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.jqxx.cloud</groupId>
    <artifactId>jqxx-dependencies</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>

    <name>${project.artifactId}</name>
    <description>基础 bom 文件，管理整个项目的依赖版本</description>
    <url>https://github.com/YunaiV/ruoyi-vue-pro</url>

    <properties>
        <revision>1.8.3-snapshot</revision>
        <flatten-maven-plugin.version>1.5.0</flatten-maven-plugin.version>
        <!-- 统一依赖管理 -->
        <spring.boot.version>2.7.17</spring.boot.version>
        <spring.cloud.version>2021.0.5</spring.cloud.version>
        <spring.cloud.alibaba.version>2021.0.4.0</spring.cloud.alibaba.version>
        <!-- Web 相关 -->
        <servlet.versoin>2.5</servlet.versoin>
        <springdoc.version>1.6.15</springdoc.version>
        <knife4j.version>4.3.0</knife4j.version>
        <!-- DB 相关 -->
        <druid.version>1.2.19</druid.version>
        <mybatis-plus.version>*******</mybatis-plus.version>
        <mybatis-plus-generator.version>*******</mybatis-plus-generator.version>
        <dynamic-datasource.version>3.6.1</dynamic-datasource.version>
        <mybatis-plus-join-boot-starter.version>1.4.6</mybatis-plus-join-boot-starter.version>
        <redisson.version>3.18.0</redisson.version>
        <dm8.jdbc.version>8.1.2.141</dm8.jdbc.version>
        <swagger.version>2.2.8</swagger.version>
        <!-- 消息队列 -->
        <rocketmq-spring.version>2.2.3</rocketmq-spring.version>
        <!-- RPC 相关 -->
        <!-- RPC 相关 -->
        <dubbo.version>2.7.18</dubbo.version>
        <!-- Config 配置中心相关 -->
        <apollo.version>1.9.2</apollo.version>
        <!-- Job 定时任务相关 -->
        <xxl-job.version>2.3.1</xxl-job.version>
        <!-- 服务保障相关 -->
        <lock4j.version>2.2.3</lock4j.version>
        <resilience4j.version>1.7.1</resilience4j.version>
        <!-- 监控相关 -->
        <skywalking.version>8.12.0</skywalking.version>
        <spring-boot-admin.version>2.7.10</spring-boot-admin.version>
        <opentracing.version>0.33.0</opentracing.version>
        <!-- Test 测试相关 -->
        <podam.version>7.2.11.RELEASE</podam.version>
        <jedis-mock.version>1.0.7</jedis-mock.version>
        <mockito-inline.version>4.11.0</mockito-inline.version>
        <!-- Bpm 工作流相关 -->
        <flowable.version>6.8.0</flowable.version>
        <!-- 工具类相关 -->
        <captcha-plus.version>1.0.8</captcha-plus.version>
        <jsoup.version>1.16.1</jsoup.version>
        <lombok.version>1.18.30</lombok.version>
        <mapstruct.version>1.5.5.Final</mapstruct.version>
        <hutool.version>5.8.22</hutool.version>
        <easyexcel.verion>3.3.2</easyexcel.verion>
        <velocity.version>2.3</velocity.version>
        <screw.version>1.0.5</screw.version>
        <fastjson.version>1.2.83</fastjson.version>
        <guava.version>32.1.2-jre</guava.version>
        <guice.version>5.1.0</guice.version>
        <transmittable-thread-local.version>2.14.2</transmittable-thread-local.version>
        <commons-net.version>3.9.0</commons-net.version>
        <jsch.version>0.1.55</jsch.version>
        <tika-core.version>2.7.0</tika-core.version>
        <ip2region.version>2.7.0</ip2region.version>
        <reflections.version>0.10.2</reflections.version>
        <!-- 三方云服务相关 -->
        <okio.version>3.5.0</okio.version>
        <okhttp3.version>4.11.0</okhttp3.version>
        <commons-io.version>2.11.0</commons-io.version>
        <minio.version>8.5.6</minio.version>
        <aliyun-java-sdk-core.version>4.6.4</aliyun-java-sdk-core.version>
        <aliyun-java-sdk-dysmsapi.version>2.2.1</aliyun-java-sdk-dysmsapi.version>
        <tencentcloud-sdk-java.version>3.1.853</tencentcloud-sdk-java.version>
        <justauth.version>1.0.5</justauth.version>
        <jimureport.version>1.6.1</jimureport.version>
        <xercesImpl.version>2.12.2</xercesImpl.version>
        <weixin-java.version>4.5.0</weixin-java.version>
        <paho.mqtt.version>1.2.5</paho.mqtt.version>
        <geotools.version>28.2</geotools.version>
        <geojson.version>1.14</geojson.version>
        <proj4j.version>0.1.0</proj4j.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- 统一依赖管理 -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring.boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring.cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring.cloud.alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- 业务组件 -->
            <dependency>
                <groupId>com.jqxx.cloud</groupId>
                <artifactId>jqxx-spring-boot-starter-banner</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.jqxx.cloud</groupId>
                <artifactId>jqxx-spring-boot-starter-biz-operatelog</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.jqxx.cloud</groupId>
                <artifactId>jqxx-spring-boot-starter-biz-dict</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.jqxx.cloud</groupId>
                <artifactId>jqxx-spring-boot-starter-biz-sms</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.jqxx.cloud</groupId>
                <artifactId>jqxx-spring-boot-starter-biz-pay</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.jqxx.cloud</groupId>
                <artifactId>jqxx-spring-boot-starter-biz-weixin</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.jqxx.cloud</groupId>
                <artifactId>jqxx-spring-boot-starter-biz-tenant</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.jqxx.cloud</groupId>
                <artifactId>jqxx-spring-boot-starter-biz-data-permission</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.jqxx.cloud</groupId>
                <artifactId>jqxx-spring-boot-starter-biz-social</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.jqxx.cloud</groupId>
                <artifactId>jqxx-spring-boot-starter-biz-error-code</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.jqxx.cloud</groupId>
                <artifactId>jqxx-spring-boot-starter-biz-ip</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.jqxx.cloud</groupId>
                <artifactId>jqxx-spring-boot-starter-captcha</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.jqxx.cloud</groupId>
                <artifactId>jqxx-spring-boot-starter-desensitize</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- Spring 核心 -->
            <dependency>
                <!-- 用于生成自定义的 Spring @ConfigurationProperties 配置类的说明文件 -->
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-configuration-processor</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jqxx.cloud</groupId>
                <artifactId>jqxx-spring-boot-starter-env</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- Web 相关 -->
            <dependency>
                <groupId>com.jqxx.cloud</groupId>
                <artifactId>jqxx-spring-boot-starter-web</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.jqxx.cloud</groupId>
                <artifactId>jqxx-spring-boot-starter-security</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>org.springdoc</groupId> <!-- 接口文档 UI：默认 -->
                <artifactId>springdoc-openapi-ui</artifactId>
                <version>${springdoc.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.xiaoymin</groupId> <!-- 接口文档 UI：knife4j -->
                <artifactId>knife4j-openapi3-spring-boot-starter</artifactId>
                <version>${knife4j.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.xiaoymin</groupId> <!-- 接口文档 UI：knife4j【网关专属】 -->
                <artifactId>knife4j-gateway-spring-boot-starter</artifactId>
                <version>${knife4j.version}</version>
            </dependency>

            <!-- DB 相关 -->
            <dependency>
                <groupId>com.jqxx.cloud</groupId>
                <artifactId>jqxx-spring-boot-starter-mybatis</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-webflux-ui</artifactId>
                <version>${springdoc.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-starter</artifactId>
                <version>${druid.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-generator</artifactId> <!-- 代码生成器，使用它解析表结构 -->
                <version>${mybatis-plus-generator.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>dynamic-datasource-spring-boot-starter</artifactId> <!-- 多数据源 -->
                <version>${dynamic-datasource.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.yulichang</groupId>
                <artifactId>mybatis-plus-join-boot-starter</artifactId> <!-- MyBatis 联表查询 -->
                <version>${mybatis-plus-join-boot-starter.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jqxx.cloud</groupId>
                <artifactId>jqxx-spring-boot-starter-redis</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-boot-starter</artifactId>
                <version>${redisson.version}</version>
            </dependency>

            <dependency>
                <groupId>com.dameng</groupId>
                <artifactId>DmJdbcDriver18</artifactId>
                <version>${dm8.jdbc.version}</version>
            </dependency>

            <!-- RPC 远程调用相关 -->
            <dependency>
                <groupId>com.jqxx.cloud</groupId>
                <artifactId>jqxx-spring-boot-starter-rpc</artifactId>
                <version>${revision}</version>
            </dependency>
            <!-- RPC 远程调用相关 -->
            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo-common</artifactId> <!-- 兜底，保证在不引入 spring-cloud-starter-dubbo 时，注解等不报错 -->
                <version>${dubbo.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo-cluster</artifactId> <!-- 兜底，保证在不引入 spring-cloud-starter-dubbo 时，注解等不报错 -->
                <version>${dubbo.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo-rpc-api</artifactId> <!-- 兜底，保证在不引入 spring-cloud-starter-dubbo 时，注解等不报错 -->
                <version>${dubbo.version}</version>
            </dependency>
            <!-- Registry 注册中心相关 -->

            <!-- Config 配置中心相关 -->

            <!-- Job 定时任务相关 -->
            <dependency>
                <groupId>com.xuxueli</groupId>
                <artifactId>xxl-job-core</artifactId>
                <version>${xxl-job.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jqxx.cloud</groupId>
                <artifactId>jqxx-spring-boot-starter-job</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 消息队列相关 -->
            <dependency>
                <groupId>com.jqxx.cloud</groupId>
                <artifactId>jqxx-spring-boot-starter-mq</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.rocketmq</groupId>
                <artifactId>rocketmq-spring-boot-starter</artifactId>
                <version>${rocketmq-spring.version}</version>
            </dependency>

            <!-- 服务保障相关 -->
            <dependency>
                <groupId>com.jqxx.cloud</groupId>
                <artifactId>jqxx-spring-boot-starter-protection</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>lock4j-redisson-spring-boot-starter</artifactId>
                <version>${lock4j.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>redisson-spring-boot-starter</artifactId>
                        <groupId>org.redisson</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>io.github.resilience4j</groupId>
                <artifactId>resilience4j-ratelimiter</artifactId>
                <version>${resilience4j.version}</version>
            </dependency>
            <dependency>
                <groupId>io.github.resilience4j</groupId>
                <artifactId>resilience4j-spring-boot2</artifactId>
                <version>${resilience4j.version}</version>
            </dependency>

            <!-- 监控相关 -->
            <dependency>
                <groupId>com.jqxx.cloud</groupId>
                <artifactId>jqxx-spring-boot-starter-monitor</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.skywalking</groupId>
                <artifactId>apm-toolkit-trace</artifactId>
                <version>${skywalking.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.skywalking</groupId>
                <artifactId>apm-toolkit-logback-1.x</artifactId>
                <version>${skywalking.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.skywalking</groupId>
                <artifactId>apm-toolkit-opentracing</artifactId>
                <version>${skywalking.version}</version>
                <!--                <exclusions>-->
                <!--                    <exclusion>-->
                <!--                        <artifactId>opentracing-api</artifactId>-->
                <!--                        <groupId>io.opentracing</groupId>-->
                <!--                    </exclusion>-->
                <!--                    <exclusion>-->
                <!--                        <artifactId>opentracing-util</artifactId>-->
                <!--                        <groupId>io.opentracing</groupId>-->
                <!--                    </exclusion>-->
                <!--                </exclusions>-->
            </dependency>
            <dependency>
                <groupId>io.opentracing</groupId>
                <artifactId>opentracing-api</artifactId>
                <version>${opentracing.version}</version>
            </dependency>
            <dependency>
                <groupId>io.opentracing</groupId>
                <artifactId>opentracing-util</artifactId>
                <version>${opentracing.version}</version>
            </dependency>
            <dependency>
                <groupId>io.opentracing</groupId>
                <artifactId>opentracing-noop</artifactId>
                <version>${opentracing.version}</version>
            </dependency>

            <dependency>
                <groupId>de.codecentric</groupId>
                <artifactId>spring-boot-admin-starter-server</artifactId> <!-- 实现 Spring Boot Admin Server 服务端 -->
                <version>${spring-boot-admin.version}</version>
            </dependency>
            <dependency>
                <groupId>de.codecentric</groupId>
                <artifactId>spring-boot-admin-starter-client</artifactId> <!-- 实现 Spring Boot Admin Server 服务端 -->
                <version>${spring-boot-admin.version}</version>
            </dependency>

            <!-- Test 测试相关 -->
            <dependency>
                <groupId>com.jqxx.cloud</groupId>
                <artifactId>jqxx-spring-boot-starter-test</artifactId>
                <version>${revision}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-inline</artifactId>
                <version>${mockito-inline.version}</version> <!-- 支持 Mockito 的 final 类与 static 方法的 mock -->
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-test</artifactId>
                <version>${spring.boot.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>asm</artifactId>
                        <groupId>org.ow2.asm</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.mockito</groupId>
                        <artifactId>mockito-core</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.github.fppt</groupId> <!-- 单元测试，我们采用内嵌的 Redis 数据库 -->
                <artifactId>jedis-mock</artifactId>
                <version>${jedis-mock.version}</version>
            </dependency>

            <dependency>
                <groupId>uk.co.jemos.podam</groupId> <!-- 单元测试，随机生成 POJO 类 -->
                <artifactId>podam</artifactId>
                <version>${podam.version}</version>
            </dependency>

            <!-- 工作流相关 -->
            <dependency>
                <groupId>com.jqxx.cloud</groupId>
                <artifactId>jqxx-spring-boot-starter-flowable</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-spring-boot-starter-process</artifactId>
                <version>${flowable.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-spring-boot-starter-actuator</artifactId>
                <version>${flowable.version}</version>
            </dependency>
            <!-- 工作流相关结束 -->

            <!-- 工具类相关 -->
            <dependency>
                <groupId>com.jqxx.cloud</groupId>
                <artifactId>jqxx-common</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.jqxx.cloud</groupId>
                <artifactId>jqxx-spring-boot-starter-excel</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>

            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId> <!-- use mapstruct-jdk8 for Java 8 or higher -->
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-jdk8</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>${easyexcel.verion}</version>
            </dependency>

            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons-io.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.tika</groupId>
                <artifactId>tika-core</artifactId> <!-- 文件类型的识别 -->
                <version>${tika-core.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.velocity</groupId>
                <artifactId>velocity-engine-core</artifactId>
                <version>${velocity.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.smallbun.screw</groupId>
                <artifactId>screw-core</artifactId> <!-- 实现数据库文档 -->
                <version>${screw.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.freemarker</groupId>
                        <artifactId>freemarker</artifactId> <!-- 移除 Freemarker 依赖，采用 Velocity 作为模板引擎 -->
                    </exclusion>
                    <exclusion>
                        <groupId>com.alibaba</groupId>
                        <artifactId>fastjson</artifactId> <!-- 最新版screw-core1.0.5依赖fastjson1.2.73存在漏洞，移除。 -->
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>

            <dependency>
                <groupId>com.google.inject</groupId>
                <artifactId>guice</artifactId>
                <version>${guice.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>transmittable-thread-local</artifactId> <!-- 解决 ThreadLocal 父子线程的传值问题 -->
                <version>${transmittable-thread-local.version}</version>
            </dependency>

            <dependency>
                <groupId>commons-net</groupId>
                <artifactId>commons-net</artifactId> <!-- 解决 ftp 连接 -->
                <version>${commons-net.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jcraft</groupId>
                <artifactId>jsch</artifactId> <!-- 解决 sftp 连接 -->
                <version>${jsch.version}</version>
            </dependency>

            <dependency>
                <groupId>com.xingyuv</groupId>
                <artifactId>spring-boot-starter-captcha-plus</artifactId>
                <version>${captcha-plus.version}</version>
            </dependency>

            <dependency>
                <groupId>org.lionsoul</groupId>
                <artifactId>ip2region</artifactId>
                <version>${ip2region.version}</version>
            </dependency>

            <dependency>
                <groupId>org.jsoup</groupId>
                <artifactId>jsoup</artifactId>
                <version>${jsoup.version}</version>
            </dependency>

            <dependency>
                <groupId>org.reflections</groupId>
                <artifactId>reflections</artifactId>
                <version>${reflections.version}</version>
            </dependency>

            <!-- 三方云服务相关 -->
            <dependency>
                <groupId>com.squareup.okio</groupId>
                <artifactId>okio</artifactId>
                <version>${okio.version}</version>
            </dependency>
            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>${okhttp3.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jqxx.cloud</groupId>
                <artifactId>jqxx-spring-boot-starter-file</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.minio</groupId>
                <artifactId>minio</artifactId>
                <version>${minio.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.binarywang</groupId>
                <artifactId>weixin-java-pay</artifactId>
                <version>${weixin-java.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.binarywang</groupId>
                <artifactId>weixin-java-mp</artifactId>
                <version>${weixin-java.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.binarywang</groupId>
                <artifactId>wx-java-mp-spring-boot-starter</artifactId>
                <version>${weixin-java.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.binarywang</groupId>
                <artifactId>wx-java-miniapp-spring-boot-starter</artifactId>
                <version>${weixin-java.version}</version>
            </dependency>

            <!-- SMS SDK begin -->
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>aliyun-java-sdk-core</artifactId>
                <version>${aliyun-java-sdk-core.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>opentracing-api</artifactId>
                        <groupId>io.opentracing</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>opentracing-util</artifactId>
                        <groupId>io.opentracing</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>aliyun-java-sdk-dysmsapi</artifactId>
                <version>${aliyun-java-sdk-dysmsapi.version}</version>
            </dependency>
            <dependency>
                <groupId>com.tencentcloudapi</groupId>
                <artifactId>tencentcloud-sdk-java-sms</artifactId>
                <version>${tencentcloud-sdk-java.version}</version>
            </dependency>
            <!-- SMS SDK end -->

            <dependency>
                <groupId>com.xingyuv</groupId>
                <artifactId>spring-boot-starter-justauth</artifactId> <!-- 社交登陆（例如说，个人微信、企业微信等等） -->
                <version>${justauth.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>cn.hutool</groupId>
                        <artifactId>hutool-core</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- 积木报表-->
            <dependency>
                <groupId>org.jeecgframework.jimureport</groupId>
                <artifactId>jimureport-spring-boot-starter</artifactId>
                <version>${jimureport.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.alibaba</groupId>
                        <artifactId>druid</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>xerces</groupId>
                <artifactId>xercesImpl</artifactId>
                <version>${xercesImpl.version}</version>
            </dependency>

            <dependency>
                <groupId>io.swagger.core.v3</groupId> <!-- 接口文档：使用最新版本的 Swagger 模型 -->
                <artifactId>swagger-annotations</artifactId>
                <version>${swagger.version}</version>
            </dependency>

            <!-- emqx mqtt服务依赖 -->
            <dependency>
                <groupId>org.eclipse.paho</groupId>
                <artifactId>org.eclipse.paho.client.mqttv3</artifactId>
                <version>${paho.mqtt.version}</version>
            </dependency>

            <dependency>
                <groupId>org.geotools</groupId>
                <artifactId>gt-main</artifactId>
                <version>${geotools.version}</version>
            </dependency>

            <!-- geotools-geojson核心包 -->
            <dependency>
                <groupId>org.geotools</groupId>
                <artifactId>gt-geojson-core</artifactId>
                <version>${geotools.version}</version>
            </dependency>

            <dependency>
                <groupId>org.geotools</groupId>
                <artifactId>gt-geojson</artifactId>
                <version>${geotools.version}</version>
            </dependency>

            <dependency>
                <groupId>de.grundid.opendatalab</groupId>
                <artifactId>geojson-jackson</artifactId>
                <version>${geojson.version}</version>
            </dependency>

            <dependency>
                <groupId>org.osgeo</groupId>
                <artifactId>proj4j</artifactId>
                <version>${proj4j.version}</version>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <!-- 统一 revision 版本 -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>${flatten-maven-plugin.version}</version>
                <configuration>
                    <flattenMode>resolveCiFriendliesOnly</flattenMode>
                    <updatePomFile>true</updatePomFile>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                    </execution>
                    <execution>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                        <id>flatten.clean</id>
                        <phase>clean</phase>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
