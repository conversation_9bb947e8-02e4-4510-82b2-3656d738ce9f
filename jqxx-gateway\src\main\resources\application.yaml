spring:
  main:
    allow-circular-references: true # 允许循环依赖，因为项目是三层架构，无法避免这个情况。

  cloud:
    # Spring Cloud Gateway 配置项，对应 GatewayProperties 类
    gateway:
      # 路由配置项，对应 RouteDefinition 数组
      routes:
        ## system-server 服务
        - id: system-admin-api # 路由的编号
          uri: grayLb://system-server
          predicates: # 断言，作为路由的匹配条件，对应 RouteDefinition 数组
            - Path=/admin-api/system/**
          filters:
            - RewritePath=/admin-api/system/v3/api-docs, /v3/api-docs # 配置，保证转发到 /v3/api-docs
        - id: system-app-api # 路由的编号
          uri: grayLb://system-server
          predicates: # 断言，作为路由的匹配条件，对应 RouteDefinition 数组
            - Path=/app-api/system/**
          filters:
            - RewritePath=/app-api/system/v3/api-docs, /v3/api-docs
        ## infra-server 服务
        - id: infra-admin-api # 路由的编号
          uri: grayLb://infra-server
          predicates: # 断言，作为路由的匹配条件，对应 RouteDefinition 数组
            - Path=/admin-api/infra/**
          filters:
            - RewritePath=/admin-api/infra/v3/api-docs, /v3/api-docs
        - id: infra-app-api # 路由的编号
          uri: grayLb://infra-server
          predicates: # 断言，作为路由的匹配条件，对应 RouteDefinition 数组
            - Path=/app-api/infra/**
          filters:
            - RewritePath=/app-api/infra/v3/api-docs, /v3/api-docs
        - id: infra-spring-boot-admin # 路由的编号（Spring Boot Admin）
          uri: grayLb://infra-server
          predicates: # 断言，作为路由的匹配条件，对应 RouteDefinition 数组
            - Path=/admin/**
        ## bpm-server 服务
        - id: bpm-admin-api # 路由的编号
          uri: grayLb://bpm-server
          predicates: # 断言，作为路由的匹配条件，对应 RouteDefinition 数组
            - Path=/admin-api/bpm/**
          filters:
            - RewritePath=/admin-api/bpm/v3/api-docs, /v3/api-docs
        ## report-server 服务
        - id: report-admin-api # 路由的编号
          uri: grayLb://report-server
          predicates: # 断言，作为路由的匹配条件，对应 RouteDefinition 数组
            - Path=/admin-api/report/**
          filters:
            - RewritePath=/admin-api/report/v3/api-docs, /v3/api-docs
        ## revsafetymgr-server 服务
        - id: revsafetymgr-admin-api # 路由的编号
          uri: grayLb://revsafetymgr-server
          predicates: # 断言，作为路由的匹配条件，对应 RouteDefinition 数组
            - Path=/admin-api/revsafetymgr/**
          filters:
            - RewritePath=/admin-api/revsafetymgr/v3/api-docs, /v3/api-docs # 配置，保证转发到 /v3/api-docs
        - id: revsafetymgr-app-api # 路由的编号
          uri: grayLb://revsafetymgr-server
          predicates: # 断言，作为路由的匹配条件，对应 RouteDefinition 数组
            - Path=/app-api/revsafetymgr/**
          filters:
            - RewritePath=/app-api/revsafetymgr/v3/api-docs, /v3/api-docs
        - id: revsafetymgr-rpc-api
          uri: grayLb://revsafetymgr-server
          predicates:
            - Path=/rpc-api/revsafetymgr/**
          filters:
            - RewritePath=/rpc-api/revsafetymgr/v3/api-docs, /v3/api-docs
        ## equipment-server 服务
        - id: equipment-admin-api # 路由的编号
#          uri: grayLb://equipment-server
          uri: http://*************:48085
          predicates: # 断言，作为路由的匹配条件，对应 RouteDefinition 数组
            - Path=/admin-api/equipment/**
          filters:
            - RewritePath=/admin-api/equipment/v3/api-docs, /v3/api-docs # 配置，保证转发到 /v3/api-docs
        - id: equipment-app-api # 路由的编号
#          uri: grayLb://equipment-server
          uri: http://*************:48085
          predicates: # 断言，作为路由的匹配条件，对应 RouteDefinition 数组
            - Path=/app-api/equipment/**
          filters:
            - RewritePath=/app-api/product/v3/api-docs, /v3/api-docs
        - id: equipment-rpc-api
#          uri: grayLb://equipment-server
          uri: http://*************:48085
          predicates:
            - Path=/rpc-api/equipment/**
          filters:
            - RewritePath=/rpc-api/revsafetymgr/v3/api-docs, /v3/api-docs
      x-forwarded:
        prefix-enabled: false # 避免 Swagger 重复带上额外的 /admin-api/system 前缀

knife4j:
  # 聚合 Swagger 文档，参考 https://doc.xiaominfo.com/docs/action/springcloud-gateway 文档
  gateway:
    enabled: true
    routes:
      - name: system-server
        service-name: system-server
        url: /admin-api/system/v3/api-docs
      - name: infra-server
        service-name: infra-server
        url: /admin-api/infra/v3/api-docs
      - name: revsafetymgr-server
        service-name: revsafetymgr-server
        url: /admin-api/revsafetymgr/v3/api-docs
      - name: bpm-server
        service-name: bpm-server
        url: /admin-api/bpm/v3/api-docs
      - name: equipment-server
        service-name: equipment-server
        url: /admin-api/equipment/v3/api-docs
