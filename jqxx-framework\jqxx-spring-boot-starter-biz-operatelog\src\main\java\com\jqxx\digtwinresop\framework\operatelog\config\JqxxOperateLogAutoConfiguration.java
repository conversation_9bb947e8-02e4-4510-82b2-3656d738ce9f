package com.jqxx.digtwinresop.framework.operatelog.config;

import com.jqxx.digtwinresop.framework.operatelog.core.aop.OperateLogAspect;
import com.jqxx.digtwinresop.framework.operatelog.core.service.OperateLogFrameworkService;
import com.jqxx.digtwinresop.framework.operatelog.core.service.OperateLogFrameworkServiceImpl;
import com.jqxx.digtwinresop.module.system.api.logger.OperateLogApi;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.context.annotation.Bean;

@AutoConfiguration
public class JqxxOperateLogAutoConfiguration {

    @Bean
    public OperateLogAspect operateLogAspect() {
        return new OperateLogAspect();
    }

    @Bean
    public OperateLogFrameworkService operateLogFrameworkService(OperateLogApi operateLogApi) {
        return new OperateLogFrameworkServiceImpl(operateLogApi);
    }

}
