package com.jqxx.digtwinresop.module.system.controller.admin.auth;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.jqxx.digtwinresop.framework.common.enums.CommonStatusEnum;
import com.jqxx.digtwinresop.framework.common.enums.UserTypeEnum;
import com.jqxx.digtwinresop.framework.common.pojo.CommonResult;
import com.jqxx.digtwinresop.framework.operatelog.core.annotations.OperateLog;
import com.jqxx.digtwinresop.framework.security.config.SecurityProperties;
import com.jqxx.digtwinresop.module.system.controller.admin.auth.vo.*;
import com.jqxx.digtwinresop.module.system.controller.admin.util.AESUtil;
import com.jqxx.digtwinresop.module.system.convert.auth.AuthConvert;
import com.jqxx.digtwinresop.module.system.dal.dataobject.permission.MenuDO;
import com.jqxx.digtwinresop.module.system.dal.dataobject.permission.RoleDO;
import com.jqxx.digtwinresop.module.system.dal.dataobject.user.AdminUserDO;
import com.jqxx.digtwinresop.module.system.enums.logger.LoginLogTypeEnum;
import com.jqxx.digtwinresop.module.system.service.auth.AdminAuthService;
import com.jqxx.digtwinresop.module.system.service.permission.MenuService;
import com.jqxx.digtwinresop.module.system.service.permission.PermissionService;
import com.jqxx.digtwinresop.module.system.service.permission.RoleService;
import com.jqxx.digtwinresop.module.system.service.social.SocialClientService;
import com.jqxx.digtwinresop.module.system.service.user.AdminUserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.Collections;
import java.util.List;
import java.util.Set;

import static com.jqxx.digtwinresop.framework.common.pojo.CommonResult.success;
import static com.jqxx.digtwinresop.framework.common.util.collection.CollectionUtils.convertSet;
import static com.jqxx.digtwinresop.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;
import static com.jqxx.digtwinresop.framework.security.core.util.SecurityFrameworkUtils.obtainAuthorization;

@Tag(name = "管理后台 - 认证")
@RestController
@RequestMapping("/system/auth")
@Validated
@Slf4j
public class AuthController {

    @Resource
    private AdminAuthService authService;
    @Resource
    private AdminUserService userService;
    @Resource
    private RoleService roleService;
    @Resource
    private MenuService menuService;
    @Resource
    private PermissionService permissionService;
    @Resource
    private SocialClientService socialClientService;

    @Resource
    private SecurityProperties securityProperties;

    @PostMapping("/login")
    @PermitAll
    @Operation(summary = "使用账号密码登录")
    @OperateLog(enable = false) // 避免 Post 请求被记录操作日志
    public CommonResult<AuthLoginRespVO> login(@RequestBody @Valid AuthLoginReqVO reqVO) {
        // 智能处理密码：如果是加密的就解密，如果是明文就直接使用
        String password = reqVO.getPassword();
        try {
            // 尝试解密密码
            String decryptedPassword = AESUtil.decrypt(password);
            // 如果解密成功且结果不为空，使用解密后的密码
            if (decryptedPassword != null && !decryptedPassword.trim().isEmpty()) {
                reqVO.setPassword(decryptedPassword);
            }
            // 如果解密失败或结果为空，保持原密码不变（可能是明文密码）
        } catch (Exception e) {
            // 解密失败，说明可能是明文密码，保持原密码不变
            // 不抛出异常，继续使用原密码进行登录
        }
        return success(authService.login(reqVO));
    }

    @PostMapping("/logout")
    @PermitAll
    @Operation(summary = "登出系统")
    @OperateLog(enable = false) // 避免 Post 请求被记录操作日志
    public CommonResult<Boolean> logout(HttpServletRequest request) {
        String token = obtainAuthorization(request, securityProperties.getTokenHeader());
        if (StrUtil.isNotBlank(token)) {
            authService.logout(token, LoginLogTypeEnum.LOGOUT_SELF.getType());
        }
        return success(true);
    }

    @PostMapping("/refresh-token")
    @PermitAll
    @Operation(summary = "刷新令牌")
    @Parameter(name = "refreshToken", description = "刷新令牌", required = true)
    @OperateLog(enable = false) // 避免 Post 请求被记录操作日志
    public CommonResult<AuthLoginRespVO> refreshToken(@RequestParam("refreshToken") String refreshToken) {
        return success(authService.refreshToken(refreshToken));
    }

    @GetMapping("/get-permission-info")
    @Operation(summary = "获取登录用户的权限信息")
    public CommonResult<AuthPermissionInfoRespVO> getPermissionInfo() {
        // 1.1 获得用户信息
        AdminUserDO user = userService.getUser(getLoginUserId());
        if (user == null) {
            return null;
        }

        // 1.2 获得角色列表
        Set<Long> roleIds = permissionService.getUserRoleIdListByUserId(getLoginUserId());
        if (CollUtil.isEmpty(roleIds)) {
            return success(AuthConvert.INSTANCE.convert(user, Collections.emptyList(), Collections.emptyList()));
        }
        List<RoleDO> roles = roleService.getRoleList(roleIds);
        roles.removeIf(role -> !CommonStatusEnum.ENABLE.getStatus().equals(role.getStatus())); // 移除禁用的角色

        // 1.3 获得菜单列表
        Set<Long> menuIds = permissionService.getRoleMenuListByRoleId(convertSet(roles, RoleDO::getId));
        List<MenuDO> menuList = menuService.getMenuList(menuIds);
        menuList.removeIf(menu -> !CommonStatusEnum.ENABLE.getStatus().equals(menu.getStatus())); // 移除禁用的菜单

        // 2. 拼接结果返回
        return success(AuthConvert.INSTANCE.convert(user, roles, menuList));
    }

    // ========== 短信登录相关 ==========

    @PostMapping("/sms-login")
    @PermitAll
    @Operation(summary = "使用短信验证码登录")
    @OperateLog(enable = false) // 避免 Post 请求被记录操作日志
    public CommonResult<AuthLoginRespVO> smsLogin(@RequestBody @Valid AuthSmsLoginReqVO reqVO) {
        return success(authService.smsLogin(reqVO));
    }

    @PostMapping("/send-sms-code")
    @PermitAll
    @Operation(summary = "发送手机验证码")
    @OperateLog(enable = false) // 避免 Post 请求被记录操作日志
    public CommonResult<Boolean> sendLoginSmsCode(@RequestBody @Valid AuthSmsSendReqVO reqVO) {
        authService.sendSmsCode(reqVO);
        return success(true);
    }

    // ========== 社交登录相关 ==========

    @GetMapping("/social-auth-redirect")
    @PermitAll
    @Operation(summary = "社交授权的跳转")
    @Parameters({
            @Parameter(name = "type", description = "社交类型", required = true),
            @Parameter(name = "redirectUri", description = "回调路径")
    })
    public CommonResult<String> socialLogin(@RequestParam("type") Integer type,
                                            @RequestParam("redirectUri") String redirectUri) {
        return success(socialClientService.getAuthorizeUrl(
                type, UserTypeEnum.ADMIN.getValue(), redirectUri));
    }

    @PostMapping("/social-login")
    @PermitAll
    @Operation(summary = "社交快捷登录，使用 code 授权码", description = "适合未登录的用户，但是社交账号已绑定用户")
    @OperateLog(enable = false) // 避免 Post 请求被记录操作日志
    public CommonResult<AuthLoginRespVO> socialQuickLogin(@RequestBody @Valid AuthSocialLoginReqVO reqVO) {
        return success(authService.socialLogin(reqVO));
    }

}
