package com.jqxx.digtwinresop.module.system.controller.admin.util;

public class AESTest {
    public static void main(String[] args) throws Exception {
        String salt = "2b7e151628aed2a6abf7158809cf4f3c";
        String key = "603deb1015ca71be2b73aef0857d778140873d0a";

//        String encrypted = AESUtil.encrypt("test123", key, salt);
//        System.out.println("加密结果: " + encrypted); // 应与前端一致

//        String decrypted = AESUtil.decrypt("", key, salt);
//        System.out.println("解密结果: " + decrypted); // 应输出：test123
    }
}
