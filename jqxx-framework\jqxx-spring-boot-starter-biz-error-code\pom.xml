<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>jqxx-framework</artifactId>
        <groupId>com.jqxx.cloud</groupId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>jqxx-spring-boot-starter-biz-error-code</artifactId>
    <packaging>jar</packaging>

    <name>${project.artifactId}</name>
    <description>
        错误码 ErrorCode 的自动配置功能，提供如下功能：
        1. 远程读取：项目启动时，从 system-server 服务，读取数据库中的 ErrorCode 错误码，实现错误码的提示可配置；
        2. 自动更新：管理员在管理后台修数据库中的 ErrorCode 错误码时，项目自动从 system-server 服务加载最新的 ErrorCode 错误码；
        3. 自动写入：项目启动时，将项目本地的错误码写到 system-server 服务中，方便管理员在管理后台编辑；
    </description>
    <url>https://github.com/YunaiV/ruoyi-vue-pro</url>

    <dependencies>
        <dependency>
            <groupId>com.jqxx.cloud</groupId>
            <artifactId>jqxx-common</artifactId>
        </dependency>

        <!-- Spring 核心 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>

        <!-- RPC 远程调用相关 -->
        <dependency>
            <groupId>com.jqxx.cloud</groupId>
            <artifactId>jqxx-spring-boot-starter-rpc</artifactId>
            <optional>true</optional>
        </dependency>

        <!-- 业务组件 -->
        <dependency>
            <groupId>com.jqxx.cloud</groupId>
            <artifactId>jqxx-module-system-api</artifactId> <!-- 需要使用它，进行操作日志的记录 -->
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>jakarta.validation</groupId>
            <artifactId>jakarta.validation-api</artifactId>
            <scope>provided</scope> <!-- 设置为 provided，主要是 ErrorCodeProperties 使用到 -->
        </dependency>
    </dependencies>

</project>
