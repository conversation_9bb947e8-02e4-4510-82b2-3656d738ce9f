<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AES加密登录演示</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"], input[type="password"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 4px;
            border-left: 4px solid #007bff;
        }
        .error {
            border-left-color: #dc3545;
            background-color: #f8d7da;
        }
        .success {
            border-left-color: #28a745;
            background-color: #d4edda;
        }
        pre {
            background: #f4f4f4;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>AES加密登录演示</h1>
        <p>这个演示页面展示了如何使用AES加密密码，与后端Java代码保持一致。</p>
        
        <form id="loginForm">
            <div class="form-group">
                <label for="username">用户名:</label>
                <input type="text" id="username" value="admin" required>
            </div>
            
            <div class="form-group">
                <label for="password">密码:</label>
                <input type="password" id="password" value="admin123" required>
            </div>
            
            <button type="button" onclick="testEncryption()">测试加密</button>
            <button type="button" onclick="simulateLogin()">模拟登录</button>
        </form>
        
        <div id="result"></div>
    </div>

    <!-- 引入crypto-js库 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/crypto-js/4.1.1/crypto-js.min.js"></script>
    
    <script>
        // AES工具类 - 与后端保持一致
        class AESUtil {
            // 与后端保持一致的常量
            static FIXED_SALT = "2b7e151628aed2a6abf7158809cf4f3c";
            static SECRET_KEY = "603deb1015ca71be2b73aef0857d778140873d0a";

            /**
             * 加密密码
             * @param {string} plainText 明文密码
             * @returns {string} 加密后的Base64字符串
             */
            static encrypt(plainText) {
                try {
                    // 将密钥和IV转换为UTF-8字节（与后端保持一致）
                    const key = CryptoJS.enc.Utf8.parse(this.SECRET_KEY);
                    const iv = CryptoJS.enc.Utf8.parse(this.FIXED_SALT);

                    // 使用AES-CBC模式加密
                    const encrypted = CryptoJS.AES.encrypt(plainText, key, {
                        iv: iv,
                        mode: CryptoJS.mode.CBC,
                        padding: CryptoJS.pad.Pkcs7
                    });

                    // 返回Base64编码的结果
                    return encrypted.toString();
                } catch (error) {
                    console.error('AES加密失败:', error);
                    throw new Error('密码加密失败');
                }
            }

            /**
             * 解密密码 - 用于测试验证
             * @param {string} encryptedData 加密的Base64字符串
             * @returns {string} 解密后的明文
             */
            static decrypt(encryptedData) {
                try {
                    // 将密钥和IV转换为UTF-8字节
                    const key = CryptoJS.enc.Utf8.parse(this.SECRET_KEY);
                    const iv = CryptoJS.enc.Utf8.parse(this.FIXED_SALT);

                    // 解密
                    const decrypted = CryptoJS.AES.decrypt(encryptedData, key, {
                        iv: iv,
                        mode: CryptoJS.mode.CBC,
                        padding: CryptoJS.pad.Pkcs7
                    });

                    // 转换为UTF-8字符串
                    return decrypted.toString(CryptoJS.enc.Utf8);
                } catch (error) {
                    console.error('AES解密失败:', error);
                    throw new Error('密码解密失败');
                }
            }
        }

        // 测试加密功能
        function testEncryption() {
            const password = document.getElementById('password').value;
            const resultDiv = document.getElementById('result');
            
            try {
                // 加密
                const encrypted = AESUtil.encrypt(password);
                
                // 解密验证
                const decrypted = AESUtil.decrypt(encrypted);
                
                const isValid = password === decrypted;
                
                resultDiv.innerHTML = `
                    <div class="result ${isValid ? 'success' : 'error'}">
                        <h3>加密测试结果</h3>
                        <p><strong>原始密码:</strong> ${password}</p>
                        <p><strong>加密后:</strong></p>
                        <pre>${encrypted}</pre>
                        <p><strong>解密后:</strong> ${decrypted}</p>
                        <p><strong>验证结果:</strong> ${isValid ? '✅ 成功' : '❌ 失败'}</p>
                        <p><strong>密钥长度:</strong> ${AESUtil.SECRET_KEY.length} 字符</p>
                        <p><strong>IV长度:</strong> ${AESUtil.FIXED_SALT.length} 字符</p>
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="result error">
                        <h3>加密失败</h3>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }

        // 模拟登录请求
        function simulateLogin() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const resultDiv = document.getElementById('result');
            
            try {
                // 加密密码
                const encryptedPassword = AESUtil.encrypt(password);
                
                // 构建登录数据
                const loginData = {
                    username: username,
                    password: encryptedPassword
                };
                
                resultDiv.innerHTML = `
                    <div class="result success">
                        <h3>模拟登录请求</h3>
                        <p><strong>请求URL:</strong> POST /system/auth/login</p>
                        <p><strong>请求头:</strong></p>
                        <pre>Content-Type: application/json</pre>
                        <p><strong>请求体:</strong></p>
                        <pre>${JSON.stringify(loginData, null, 2)}</pre>
                        <p><strong>说明:</strong> 密码已加密，可以安全传输到后端</p>
                        <p><strong>后端将使用AESUtil.decrypt()方法解密密码</strong></p>
                    </div>
                `;
                
                console.log('登录数据:', loginData);
                
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="result error">
                        <h3>登录失败</h3>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }

        // 页面加载完成后自动测试
        window.onload = function() {
            console.log('AES工具类已加载');
            console.log('SECRET_KEY:', AESUtil.SECRET_KEY);
            console.log('FIXED_SALT:', AESUtil.FIXED_SALT);
        };
    </script>
</body>
</html>
