package com.jqxx.digtwinresop.module.system.controller.admin.util;

import org.bouncycastle.jce.provider.BouncyCastleProvider;
import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.Security;
import java.util.Arrays;
import java.util.Base64;

public class AESUtil {
    private static final String FIXED_SALT = "2b7e151628aed2a6abf7158809cf4f3c".substring(0, 32); // 与前端一致，截取为128位(32字符)
    private static final String SECRET_KEY = "603deb1015ca71be2b73aef0857d77814"; // 与前端一致，截取为128位(32字符)

    static {
        Security.addProvider(new BouncyCastleProvider());
    }


    private static byte[] hexToBytes(String hex) {
        if (hex == null || hex.length() % 2 != 0) {
            throw new IllegalArgumentException("Invalid hex string: " + hex);
        }
        int len = hex.length();
        byte[] data = new byte[len / 2];
        for (int i = 0; i < len; i += 2) {
            int digit1 = Character.digit(hex.charAt(i), 16);
            int digit2 = Character.digit(hex.charAt(i + 1), 16);
            if (digit1 == -1 || digit2 == -1) {
                throw new IllegalArgumentException("Invalid hex character at position " + i + " in: " + hex);
            }
            data[i / 2] = (byte) ((digit1 << 4) + digit2);
        }
        return data;
    }

    public static String decrypt(String encryptedData) throws Exception {
        if (encryptedData == null || encryptedData.trim().isEmpty()) {
            throw new IllegalArgumentException("Encrypted data cannot be null or empty");
        }

        byte[] keyBytes = hexToBytes(SECRET_KEY); // 转换后才是合法长度
        byte[] ivBytes = Arrays.copyOf(hexToBytes(FIXED_SALT), 16);
        IvParameterSpec ivSpec = new IvParameterSpec(ivBytes);

        Cipher cipher = Cipher.getInstance("AES/CBC/PKCS7Padding", "BC");
        SecretKeySpec keySpec = new SecretKeySpec(keyBytes, "AES");

        cipher.init(Cipher.DECRYPT_MODE, keySpec, ivSpec);
        byte[] decrypted = cipher.doFinal(Base64.getDecoder().decode(encryptedData));
        return new String(decrypted, StandardCharsets.UTF_8);
    }

    // 用于调试的方法
    public static void validateConstants() {
        System.out.println("FIXED_SALT length: " + FIXED_SALT.length());
        System.out.println("SECRET_KEY length: " + SECRET_KEY.length());
        try {
            byte[] saltBytes = hexToBytes(FIXED_SALT);
            byte[] keyBytes = hexToBytes(SECRET_KEY);
            System.out.println("FIXED_SALT bytes length: " + saltBytes.length);
            System.out.println("SECRET_KEY bytes length: " + keyBytes.length);
        } catch (Exception e) {
            System.err.println("Error validating constants: " + e.getMessage());
            e.printStackTrace();
        }
    }

}
