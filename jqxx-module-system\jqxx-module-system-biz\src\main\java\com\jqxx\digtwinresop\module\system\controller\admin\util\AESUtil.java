package com.jqxx.digtwinresop.module.system.controller.admin.util;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.security.Security;
import java.util.Base64;

public class AESUtil {
    private static final String FIXED_SALT = "2b7e151628aed2a6abf7158809cf4f3c"; // 与前端一致
    private static final String SECRET_KEY = "603deb1015ca71be2b73aef0857d778140873d0a"; // 与前端一致

    static {
        Security.addProvider(new BouncyCastleProvider());
    }

    public static String decrypt(String encryptedData) throws Exception {
        // 密钥和IV处理
        byte[] keyBytesRaw = SECRET_KEY.getBytes("UTF-8");
        byte[] saltBytes = FIXED_SALT.getBytes("UTF-8");

        // 确保密钥长度为32字节（256位AES）
        byte[] keyBytes = new byte[32];
        System.arraycopy(keyBytesRaw, 0, keyBytes, 0, Math.min(keyBytesRaw.length, 32));

        // 确保IV长度为16字节
        byte[] ivBytes = new byte[16];
        System.arraycopy(saltBytes, 0, ivBytes, 0, Math.min(saltBytes.length, 16));

        // 解密配置
        Cipher cipher = Cipher.getInstance("AES/CBC/PKCS7Padding", "BC");
        SecretKeySpec keySpec = new SecretKeySpec(keyBytes, "AES");
        IvParameterSpec ivSpec = new IvParameterSpec(ivBytes);

        cipher.init(Cipher.DECRYPT_MODE, keySpec, ivSpec);
        byte[] decrypted = cipher.doFinal(Base64.getDecoder().decode(encryptedData));
        return new String(decrypted, "UTF-8");
    }
}



