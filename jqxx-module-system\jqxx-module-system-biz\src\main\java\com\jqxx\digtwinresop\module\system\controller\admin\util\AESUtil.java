package com.jqxx.digtwinresop.module.system.controller.admin.util;

import org.bouncycastle.jce.provider.BouncyCastleProvider;
import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.security.Security;
import java.util.Base64;

public class AESUtil {
    private static final String FIXED_SALT = "c5f8h2j9l4n7p1q6"; // 与前端一致
    private static final String SECRET_KEY = "k9m2n8p4q7r3s6t1u5v9w2x8y4z7a3b6"; // 与前端一致

    static {
        Security.addProvider(new BouncyCastleProvider());
    }

    /**
     * 判断字符串是否为Base64编码的加密数据
     */
    public static boolean isEncryptedPassword(String password) {
        if (password == null || password.trim().isEmpty()) {
            return false;
        }

        // 简单的Base64格式检查
        try {
            // Base64字符串通常比较长，且只包含Base64字符
            if (password.length() < 16) {
                return false; // 太短，不太可能是加密数据
            }

            // 检查是否包含Base64字符集之外的字符
            if (!password.matches("^[A-Za-z0-9+/]*={0,2}$")) {
                return false;
            }

            // 尝试Base64解码，如果失败说明不是Base64
            Base64.getDecoder().decode(password);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 智能解密：如果是加密数据就解密，否则返回原文
     */
    public static String smartDecrypt(String password) {
        if (!isEncryptedPassword(password)) {
            return password; // 不是加密数据，直接返回
        }

        try {
            return decrypt(password);
        } catch (Exception e) {
            // 解密失败，返回原密码
            return password;
        }
    }

    public static String decrypt(String encryptedData) throws Exception {
        // 密钥和IV处理
        byte[] keyBytes = SECRET_KEY.getBytes("UTF-8");
        byte[] ivBytes = FIXED_SALT.getBytes("UTF-8");

        // 解密配置
        Cipher cipher = Cipher.getInstance("AES/CBC/PKCS7Padding", "BC");
        SecretKeySpec keySpec = new SecretKeySpec(keyBytes, "AES");
        IvParameterSpec ivSpec = new IvParameterSpec(ivBytes);

        cipher.init(Cipher.DECRYPT_MODE, keySpec, ivSpec);
        byte[] decrypted = cipher.doFinal(Base64.getDecoder().decode(encryptedData));
        return new String(decrypted, "UTF-8");
    }
}
