package com.jqxx.digtwinresop.module.system.controller.admin.util;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.security.Security;
import java.util.Base64;

public class AESUtil {
    private static final String FIXED_SALT = "2b7e151628aed2a6abf7158809cf4f3c"; // 与前端一致
    private static final String SECRET_KEY = "603deb1015ca71be2b73aef0857d778140873d0a"; // 与前端一致

    static {
        Security.addProvider(new BouncyCastleProvider());
    }

    public static String decrypt(String encryptedData,String key,String salt) throws Exception {
        // 密钥和IV处理
        byte[] keyBytes = SECRET_KEY.getBytes("UTF-8");
        byte[] ivBytes = FIXED_SALT.getBytes("UTF-8");

        // 解密配置
        Cipher cipher = Cipher.getInstance("AES/CBC/PKCS7Padding", "BC");
        SecretKeySpec keySpec = new SecretKeySpec(keyBytes, "AES");
        IvParameterSpec ivSpec = new IvParameterSpec(ivBytes);

        cipher.init(Cipher.DECRYPT_MODE, keySpec, ivSpec);
        byte[] decrypted = cipher.doFinal(Base64.getDecoder().decode(encryptedData));
        return new String(decrypted, "UTF-8");
    }
}


public class AESTest {
    public static void main(String[] args) throws Exception {
        String salt = "2b7e151628aed2a6abf7158809cf4f3c";
        String key = "603deb1015ca71be2b73aef0857d778140873d0a";

//        String encrypted = AESUtil.encrypt("test123", key, salt);
//        System.out.println("加密结果: " + encrypted); // 应与前端一致

        String decrypted = AESUtil.decrypt("", key, salt);
        System.out.println("解密结果: " + decrypted); // 应输出：test123
    }
}
