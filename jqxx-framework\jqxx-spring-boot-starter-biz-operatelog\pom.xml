<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.jqxx.cloud</groupId>
        <artifactId>jqxx-framework</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>jqxx-spring-boot-starter-biz-operatelog</artifactId>
    <packaging>jar</packaging>

    <name>${project.artifactId}</name>
    <description>操作日志</description>
    <url>https://github.com/YunaiV/ruoyi-vue-pro</url>

    <dependencies>
        <dependency>
            <groupId>com.jqxx.cloud</groupId>
            <artifactId>jqxx-common</artifactId>
        </dependency>

        <!-- Spring 核心 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>

        <!-- Web 相关 -->
        <dependency>
            <groupId>com.jqxx.cloud</groupId>
            <artifactId>jqxx-spring-boot-starter-web</artifactId>
            <scope>provided</scope>
        </dependency>

        <!-- RPC 远程调用相关 -->
        <dependency>
            <groupId>com.jqxx.cloud</groupId>
            <artifactId>jqxx-spring-boot-starter-rpc</artifactId>
            <optional>true</optional>
        </dependency>

        <!-- 业务组件 -->
        <dependency>
            <groupId>com.jqxx.cloud</groupId>
            <artifactId>jqxx-module-system-api</artifactId> <!-- 需要使用它，进行操作日志的记录 -->
            <version>${revision}</version>
        </dependency>

        <!-- 工具类相关 -->
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>
    </dependencies>

</project>
